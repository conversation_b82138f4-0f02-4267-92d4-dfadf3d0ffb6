{"url": "http://localhost:8080/index.php/post/3", "method": "PUT", "isAJAX": false, "startTime": **********.400793, "totalTime": 172.9, "totalMemory": "6.331", "segmentDuration": 25, "segmentCount": 7, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.412587, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.465405, "duration": 0.007295131683349609}, {"name": "Routing", "component": "Timer", "start": **********.472712, "duration": 0.002129077911376953}, {"name": "Before Filters", "component": "Timer", "start": **********.475615, "duration": 0.0024938583374023438}, {"name": "Controller", "component": "Timer", "start": **********.478113, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.478114, "duration": 0.007283210754394531}, {"name": "After Filters", "component": "Timer", "start": **********.572016, "duration": 4.506111145019531e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.572087, "duration": 0.0016689300537109375}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(2 total Queries, 2 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n<strong>WHERE</strong> `artikel`.`id` = &#039;3&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:210", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:609", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Post.php:190", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Post->update()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Post.php:190", "qid": "5b49863f41eaaa9313f536b7cbd2244f"}, {"hover": "", "class": "", "duration": "2.05 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `judul` = &#039;Orang Ganteng Tidur&#039;, `isi` = &#039;<PERSON><PERSON> wajah yang hitam beliau tertidur lelap&#039;, `status` = &#039;1&#039;, `gambar` = &#039;**********_78286fc0547db89240da.jpg&#039;\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;3&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Post.php:253", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Post->update()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\Post.php:253", "qid": "69a08a6e28a808863fe6f6cc20d58a03"}]}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.505036, "duration": "0.020886"}, {"name": "Query", "component": "Database", "start": **********.527724, "duration": "0.000561", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n<strong>WHERE</strong> `artikel`.`id` = &#039;3&#039;"}, {"name": "Query", "component": "Database", "start": **********.565494, "duration": "0.002047", "query": "<strong>UPDATE</strong> `artikel` SET `judul` = &#039;Orang Ganteng Tidur&#039;, `isi` = &#039;<PERSON><PERSON> wajah yang hitam beliau tertidur lelap&#039;, `status` = &#039;1&#039;, `gambar` = &#039;**********_78286fc0547db89240da.jpg&#039;\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;3&#039;)"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 150 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Files\\File.php", "name": "File.php"}, {"path": "SYSTEMPATH\\Filters\\Cors.php", "name": "Cors.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\Format\\Format.php", "name": "Format.php"}, {"path": "SYSTEMPATH\\Format\\FormatterInterface.php", "name": "FormatterInterface.php"}, {"path": "SYSTEMPATH\\Format\\JSONFormatter.php", "name": "JSONFormatter.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Cors.php", "name": "Cors.php"}, {"path": "SYSTEMPATH\\HTTP\\Files\\FileCollection.php", "name": "FileCollection.php"}, {"path": "SYSTEMPATH\\HTTP\\Files\\UploadedFile.php", "name": "UploadedFile.php"}, {"path": "SYSTEMPATH\\HTTP\\Files\\UploadedFileInterface.php", "name": "UploadedFileInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\RESTful\\BaseResource.php", "name": "BaseResource.php"}, {"path": "SYSTEMPATH\\RESTful\\ResourceController.php", "name": "ResourceController.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Cors.php", "name": "Cors.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Format.php", "name": "Format.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Mimes.php", "name": "Mimes.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Post.php", "name": "Post.php"}, {"path": "APPPATH\\Models\\ArtikelModel.php", "name": "ArtikelModel.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 150, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Post", "method": "update", "paramCount": 1, "truePCount": 1, "params": [{"name": "$id = ", "value": "3"}]}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Page::home"}, {"method": "GET", "route": "artikel", "handler": "\\App\\Controllers\\Artikel::index"}, {"method": "GET", "route": "admin/artikel", "handler": "\\App\\Controllers\\Artikel::admin_index"}, {"method": "GET", "route": "admin/artikel/delete/(.*)", "handler": "\\App\\Controllers\\Artikel::delete/$1"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Page::about"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\Page::contact"}, {"method": "GET", "route": "faqs", "handler": "\\App\\Controllers\\Page::faqs"}, {"method": "GET", "route": "tos", "handler": "\\App\\Controllers\\Page::tos"}, {"method": "GET", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "GET", "route": "user/logout", "handler": "\\App\\Controllers\\User::logout"}, {"method": "GET", "route": "artikel/([^/]+)", "handler": "\\App\\Controllers\\Artikel::view/$1"}, {"method": "GET", "route": "post", "handler": "\\App\\Controllers\\Post::index"}, {"method": "GET", "route": "post/new", "handler": "\\App\\Controllers\\Post::new"}, {"method": "GET", "route": "post/(.*)/edit", "handler": "\\App\\Controllers\\Post::edit/$1"}, {"method": "GET", "route": "post/(.*)", "handler": "\\App\\Controllers\\Post::show/$1"}, {"method": "GET", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "GET", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "HEAD", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "HEAD", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "POST", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "POST", "route": "post", "handler": "\\App\\Controllers\\Post::create"}, {"method": "POST", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "POST", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PATCH", "route": "post/(.*)", "handler": "\\App\\Controllers\\Post::update/$1"}, {"method": "PATCH", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PATCH", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PUT", "route": "post/(.*)", "handler": "\\App\\Controllers\\Post::update/$1"}, {"method": "PUT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PUT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "DELETE", "route": "post/(.*)", "handler": "\\App\\Controllers\\Post::delete/$1"}, {"method": "DELETE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "DELETE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "OPTIONS", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "OPTIONS", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "TRACE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "TRACE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CONNECT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CONNECT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CLI", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CLI", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}]}, "badgeValue": 3, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "23.11", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.07", "count": 2}}}, "badgeValue": 3, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.442279, "duration": 0.023114919662475586}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.528293, "duration": 3.409385681152344e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.567548, "duration": 3.2901763916015625e-05}]}], "vars": {"varData": {"View Data": []}, "post": {"judul": "Orang Ganteng Tidur", "isi": "<PERSON>gan wajah yang hitam beliau tertidur lelap", "status": "1", "_method": "PUT"}, "headers": {"Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryfDDauYipvUANAkwF", "Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "application/json", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Origin": "http://localhost", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost/lab8_vuejs/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7", "Cookie": "ci_session=5034409fe66f6fb45128f031b2b49578"}, "cookies": {"ci_session": "5034409fe66f6fb45128f031b2b49578"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "application/json; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "application/json; charset=UTF-8", "Access-Control-Allow-Origin": "*"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}